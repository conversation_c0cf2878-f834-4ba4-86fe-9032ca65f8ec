import React, { useEffect, useState, useRef } from 'react'
import ReactDOM from 'react-dom'

import { CollapsibleSection } from './ui/CollapsibleSection.jsx';
import { usePromptEnhancer } from '../hooks/usePromptEnhancer.js';
import { 
  generateDarkerAccentShade, 
  getOptimalStrokeColor, 
  CORE_COLORS,
  CORE_COLOR_PALETTE,
  isCoreColor 
} from '../utils/colorUtils.js';


// Import new person setting components
import { FaceUploadSection } from './person/FaceUploadSection.jsx';
import { MoodAndExpressionPicker } from './person/MoodAndExpressionPicker.jsx';
import { GenderSelector } from './person/GenderSelector.jsx';

// Enhanced tooltip component with macOS Liquid Glass styling and precise positioning
const TooltipIcon = ({ tooltipText, tooltipId = '' }) => {
    const iconRef = React.useRef(null);
    const tooltipRef = React.useRef(null);
    const [isVisible, setIsVisible] = React.useState(false);
    const [isClosing, setIsClosing] = React.useState(false);
    const [position, setPosition] = React.useState({ top: 0, left: 0, placement: 'bottom' });
    const arrowSize = 8; // CSS variable value
    const tooltipOffset = 12; // CSS variable value
    const minEdgeDistance = 16; // CSS variable value

    const calculatePrecisePosition = React.useCallback(() => {
        if (!iconRef.current || !tooltipRef.current) return;

        const iconRect = iconRef.current.getBoundingClientRect();
        const tooltipRect = tooltipRef.current.getBoundingClientRect();
        
        // Get precise dimensions
        const tooltipWidth = tooltipRect.width || 280;
        const tooltipHeight = tooltipRect.height || 60;
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const scrollY = window.scrollY || document.documentElement.scrollTop;
        const scrollX = window.scrollX || document.documentElement.scrollLeft;
        
        // Calculate icon center for precise centering
        const iconCenterX = iconRect.left + (iconRect.width / 2);
        const iconCenterY = iconRect.top + (iconRect.height / 2);
        
        let newPosition = { top: 0, left: 0, placement: 'bottom' };

        // PRIORITY 1: Below icon (preferred placement)
        const spaceBelow = viewportHeight - iconRect.bottom;
        if (spaceBelow >= tooltipHeight + tooltipOffset + minEdgeDistance) {
            newPosition.placement = 'bottom';
            newPosition.top = iconRect.bottom + tooltipOffset + scrollY;
            newPosition.left = iconCenterX - (tooltipWidth / 2) + scrollX;
        }
        // PRIORITY 2: Above icon (if not enough space below)
        else if (iconRect.top >= tooltipHeight + tooltipOffset + minEdgeDistance) {
            newPosition.placement = 'top';
            newPosition.top = iconRect.top - tooltipHeight - tooltipOffset + scrollY;
            newPosition.left = iconCenterX - (tooltipWidth / 2) + scrollX;
        }
        // PRIORITY 3: To the right of icon
        else if (viewportWidth - iconRect.right >= tooltipWidth + tooltipOffset + minEdgeDistance) {
            newPosition.placement = 'right';
            newPosition.top = iconCenterY - (tooltipHeight / 2) + scrollY;
            newPosition.left = iconRect.right + tooltipOffset + scrollX;
        }
        // PRIORITY 4: To the left of icon (last resort)
        else {
            newPosition.placement = 'left';
            newPosition.top = iconCenterY - (tooltipHeight / 2) + scrollY;
            newPosition.left = iconRect.left - tooltipWidth - tooltipOffset + scrollX;
        }

        // VIEWPORT BOUNDARY ADJUSTMENTS for horizontal positioning
        if (newPosition.left < minEdgeDistance) {
            newPosition.left = minEdgeDistance;
        } else if (newPosition.left + tooltipWidth > viewportWidth - minEdgeDistance) {
            newPosition.left = viewportWidth - tooltipWidth - minEdgeDistance;
        }

        // VIEWPORT BOUNDARY ADJUSTMENTS for vertical positioning
        if (newPosition.top < minEdgeDistance + scrollY) {
            newPosition.top = minEdgeDistance + scrollY;
        } else if (newPosition.top + tooltipHeight > viewportHeight + scrollY - minEdgeDistance) {
            newPosition.top = viewportHeight + scrollY - tooltipHeight - minEdgeDistance;
        }

        setPosition(newPosition);
    }, [arrowSize, tooltipOffset, minEdgeDistance]);

    const handleMouseEnter = () => {
        setIsClosing(false);
        setIsVisible(true);
        // Small delay to ensure DOM update, then calculate position
        setTimeout(() => calculatePrecisePosition(), 15);
    };

    const handleMouseLeave = () => {
        setIsClosing(true);
        // Wait for the fade-out animation to complete before hiding
        setTimeout(() => {
            setIsVisible(false);
            setIsClosing(false);
        }, 300); // Match CSS transition duration
    };

    const handleFocus = () => {
        setIsClosing(false);
        setIsVisible(true);
        setTimeout(() => calculatePrecisePosition(), 15);
    };

    const handleBlur = () => {
        setIsClosing(true);
        setTimeout(() => {
            setIsVisible(false);
            setIsClosing(false);
        }, 300);
    };

    // Enhanced position recalculation on scroll and resize
    React.useEffect(() => {
        if (isVisible && !isClosing) {
            calculatePrecisePosition();
            
            const handleUpdate = () => {
                if (isVisible && !isClosing) {
                    calculatePrecisePosition();
                }
            };
            
            // More frequent recalculation for better accuracy
            const throttledUpdate = throttle(handleUpdate, 16); // ~60fps
            
            window.addEventListener('resize', throttledUpdate);
            window.addEventListener('scroll', throttledUpdate, true);
            
            return () => {
                window.removeEventListener('resize', throttledUpdate);
                window.removeEventListener('scroll', throttledUpdate, true);
            };
        }
    }, [isVisible, isClosing, calculatePrecisePosition]);

    // Enhanced arrow positioning for precise centering
    const getArrowClass = () => {
        switch (position.placement) {
            case 'top':
                return 'tooltip-arrow-bottom'; // Arrow points down when tooltip is above
            case 'bottom':
                return 'tooltip-arrow-top'; // Arrow points up when tooltip is below
            case 'left':
                return 'tooltip-arrow-right'; // Arrow points right when tooltip is to the left
            case 'right':
                return 'tooltip-arrow-left'; // Arrow points left when tooltip is to the right
            default:
                return 'tooltip-arrow-top';
        }
    };

    // Simple throttle function for performance
    const throttle = (func, limit) => {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    };

    // Create tooltip element using portal to render in document.body for absolute positioning
    const tooltipElement = isVisible ? ReactDOM.createPortal(
        React.createElement('div', {
            ref: tooltipRef,
            className: `tooltip-fixed-content tooltip-placement-${position.placement} ${isClosing ? 'tooltip-closing' : ''}`,
            style: {
                position: 'fixed',
                top: `${position.top}px`,
                left: `${position.left}px`,
                visibility: isClosing ? 'hidden' : 'visible',
                opacity: isClosing ? 0 : 1,
                zIndex: 10000
            },
            role: 'tooltip',
            'aria-hidden': isClosing,
            id: tooltipId
        },
            React.createElement('p', {
                style: { margin: 0, padding: 0 }
            }, tooltipText),
            React.createElement('div', {
                className: `tooltip-arrow-fixed ${getArrowClass()}`
            })
        ),
        document.body
    ) : null;

    return React.createElement('div', {
        className: 'tooltip-icon-wrapper relative inline-block',
        onMouseEnter: handleMouseEnter,
        onMouseLeave: handleMouseLeave,
        onFocus: handleFocus,
        onBlur: handleBlur,
        tabIndex: 0,
        'aria-label': tooltipText,
        role: 'button'
    },
        React.createElement('svg', {
            ref: iconRef,
            xmlns: 'http://www.w3.org/2000/svg',
            fill: 'none',
            viewBox: '0 0 24 24',
            stroke: 'currentColor',
            className: 'tooltip-icon w-4 h-4 text-gray-400 transition-all duration-200 cursor-help'
        },
            React.createElement('path', {
                strokeLinecap: 'round',
                strokeLinejoin: 'round',
                strokeWidth: 2,
                d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
            })
        ),
        tooltipElement
    );
};

export const ControlPanel = ({
    includePerson,
    includeIcons,
    textOverlay,
    mood,
    handleToggleChange,
    handleMoodChange,
    setIncludePerson, // Direct setters passed from App
    setIncludeIcons,
    setTextOverlay,
    selectedTextSize, // Added prop
    setSelectedTextSize, // Added prop
    fitFullCanvas, // Added prop
    setFitFullCanvas, // Added prop

    // For person settings
    selectedExpression,
    setSelectedExpression,
    selectedGender,
    setSelectedGender,
    customFaceImageUrl,
    setCustomFaceImageUrl,
    imageSourceType,
    handleImageSourceTypeChange,
    handleFileUpload,
    setErrorMsg,
    // For text settings
    overlayText,
    setOverlayText,
    isEditingOverlayText,
    setIsEditingOverlayText,
    textPosition,
    setTextPosition,
    selectedFontFamily,
    setSelectedFontFamily,
    primaryTextColor,
    setPrimaryTextColor,
    secondaryTextColor,
    setSecondaryTextColor,
    selectedPalette,
    setSelectedPalette,
    // For layout and display settings
    showLayoutSimulator,
    setShowLayoutSimulator,
    showSafeZone,
    setShowSafeZone,
    // NEW: Icon rendering mode props
    iconRenderingMode,
    setIconRenderingMode,
    // NEW: User prompt for headshot detection
    userPrompt = '',
    // NEW: Function to open image requirements modal
    openImageRequirementsModal,
    // NEW: Smart text suggestion for overlay text placeholder
    smartTextSuggestion = '',
    // NEW: Function to show success notifications
    onShowSuccessToast
}) => {

    // Initialize the prompt enhancer hook
    const { 
        isHeadshotMode, 
        updateHeadshotMode, 
        getHeadshotEnhancementDescription 
    } = usePromptEnhancer();

    // Update headshot mode when relevant props change
    useEffect(() => {
        updateHeadshotMode(includePerson, selectedExpression, userPrompt);
    }, [includePerson, selectedExpression, userPrompt]);

    // Initialize Safari text button fixes (moved from createTextSizeSelector to fix hooks rule)
    useEffect(() => {
        // Only run if textOverlay is enabled
        if (!textOverlay) return;
        
        // Dynamic import to avoid SSR issues
        import('../utils/safariTextButtonFix.js').then(({ initSafariTextButtonFixes }) => {
            const cleanup = initSafariTextButtonFixes(selectedTextSize);
            
            // Return cleanup function
            return cleanup;
        }).catch(() => {
            // Silently fail if Safari fix utility is not available
        });
    }, [textOverlay, selectedTextSize]);

    // NEW: State for overlay text refresh loading feedback (simplified)
    const [isRefreshingOverlayText, setIsRefreshingOverlayText] = useState(false);

    // Helper function to create a toggle switch
    const createToggle = (label, id, checked, setter, tooltip = null) => {
        const handleKeyDown = (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                handleToggleChange(setter);
                e.preventDefault();
            }
        };
        
        return React.createElement('div', { 
            className: 'toggle-row flex items-center justify-between py-2', 
            id: `toggle-${id}-control-row` // Enhanced with descriptive naming
        },
            React.createElement('div', { 
                className: 'toggle-label-group flex items-center gap-2',
                id: `toggle-${id}-label-group`
            },
                React.createElement('label', { 
                    htmlFor: id, 
                    className: 'glass-label toggle-label cursor-pointer',
                    id: `toggle-${id}-label` 
                }, label),
                tooltip && React.createElement(TooltipIcon, { tooltipText: tooltip, tooltipId: `tooltip-${id}-info` })
            ),
            React.createElement('button', {
                id: `toggle-${id}-switch-button`,
                role: 'switch',
                'aria-checked': checked,
                onClick: () => handleToggleChange(setter),
                onKeyDown: handleKeyDown,
                tabIndex: '0',
                className: `glass-toggle-switch ${checked ? 'active' : ''} relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-transparent`
            },
                React.createElement('span', { className: 'sr-only' }, 'Use setting'),
                React.createElement('span', {
                    'aria-hidden': 'true',
                    className: `toggle-circle ${checked ? 'translate-x-5' : 'translate-x-0'} inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`,
                    id: `toggle-${id}-circle-indicator`
                })
            )
        );
    };

    // Helper to create Text Size Button Group (Modern Tab Style)
    const createTextSizeSelector = () => {
        if (!textOverlay) return null;
        
        const sizes = ["Small", "Medium", "Large"];
        const label = "Text Size:";
        const groupId = "text-size-button-group";

        return (
            React.createElement('div', { 
                className: 'text-size-selector-section py-2', 
                id: 'text-size-selector-section' 
            },
                React.createElement('label', { 
                    className: 'glass-label text-size-label block mb-2',
                    id: 'text-size-label'
                }, label),
                React.createElement('div', { 
                    role: 'group', 
                    id: groupId, 
                    className: 'glass-button-group text-size-button-group relative flex',
                    style: {
                        minHeight: '40px',
                        width: '100%'
                    }
                },
                    // Animated background pill
                    React.createElement('div', {
                        className: 'text-size-selector-pill transition-all duration-300 ease-in-out',
                        style: {
                            transform: `translateX(${selectedTextSize === 'Small' ? '0%' : selectedTextSize === 'Medium' ? '100%' : '200%'})`
                        },
                        'aria-hidden': 'true'
                    }),
                    // Size buttons
                    sizes.map((size, index) => {
                        const isSelected = selectedTextSize === size;
                        const isFirst = index === 0;
                        const isLast = index === sizes.length - 1;
                        const isMiddle = !isFirst && !isLast;
                        
                        // Apply specific border radius based on position
                        let borderRadiusClass = '';
                        if (isFirst) {
                            borderRadiusClass = 'rounded-l-lg';
                        } else if (isLast) {
                            borderRadiusClass = 'rounded-r-lg';
                        } else if (isMiddle) {
                            borderRadiusClass = ''; // No rounded corners for middle button
                        }
                        
                        return React.createElement('button', {
                            key: size,
                            id: `text-size-button-${size.toLowerCase()}`,
                            type: 'button',
                            onClick: (e) => {
                                // Import Safari fix utility dynamically to avoid SSR issues
                                import('../utils/safariTextButtonFix.js').then(({ handleSafariButtonClick }) => {
                                    handleSafariButtonClick(size, setSelectedTextSize, e);
                                }).catch(() => {
                                    // Fallback if import fails
                                    setSelectedTextSize(size);
                                });
                            },
                            className: `text-size-button relative z-10 text-sm font-medium transition-all duration-200 ${
                                isSelected 
                                    ? 'text-white' 
                                    : 'text-gray-400 hover:text-gray-200'
                            } focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-gray-800`,
                            style: {
                                letterSpacing: '0.025em',
                                // Safari-specific style fixes
                                WebkitFontSmoothing: 'antialiased',
                                WebkitTextSizeAdjust: '100%',
                                ...(isSelected && {
                                    color: '#FFFFFF',
                                    WebkitTextFillColor: '#FFFFFF',
                                    fontWeight: '600'
                                })
                            },
                            'aria-pressed': isSelected,
                            'aria-label': `Set text size to ${size}`
                        }, size);
                    })
                )
            )
        );
    };

    // Create Mood Dropdown
    const createMoodDropdown = () => {
        const moods = ['Happy', 'Excited', 'Serious', 'Surprised']; // Available moods
        return (
            React.createElement('div', { 
                className: 'mood-selector-section py-2',
                id: 'mood-selector-section'
            }, // Added padding
                React.createElement('label', {
                    htmlFor: 'mood-select-dropdown',
                    className: 'glass-label mood-selector-label block mb-1',
                    id: 'mood-selector-label'
                }, 'Select Mood (if person included):'),
                React.createElement('div', { className: 'glass-select-wrapper' },
                    React.createElement('select', {
                        id: 'mood-select-dropdown',
                        value: mood,
                        onChange: handleMoodChange,
                        disabled: !includePerson, // Disable if includePerson is false
                        className: 'glass-select disabled:opacity-50 disabled:cursor-not-allowed' // Added disabled styles
                    },
                        moods.map(m => React.createElement('option', { 
                            key: m, 
                            value: m,
                            id: `mood-option-${m.toLowerCase()}`
                        }, m))
                    ),
                    React.createElement('span', {
                        className: 'glass-select-chevron iconify',
                        'data-icon': 'solar:alt-arrow-down-linear'
                    })
                )
            )
        );
    };

    // Create face upload section
    const createFaceUploadSection = () => {
        if (!includePerson) return null;
    
        // Use a ref for the file input to programmatically clear it
        const fileInputRef = React.useRef(null);
    
        const currentTempUrl = imageSourceType === 'url' ? customFaceImageUrl : ''; // Only prefill if type is URL and there's a URL
        const [tempUrl, setTempUrl] = React.useState(currentTempUrl);
    
        React.useEffect(() => {
            // If switching to URL mode and customFaceImageUrl is a data URL (from upload), clear tempUrl
            if (imageSourceType === 'url' && customFaceImageUrl && customFaceImageUrl.startsWith('data:image')) {
                setTempUrl('');
            } else if (imageSourceType === 'url') {
                setTempUrl(customFaceImageUrl || '');
            }
            // If switching to upload, tempUrl is not directly used for display of the uploaded image path
        }, [imageSourceType, customFaceImageUrl]);
    
        const handleSetFaceImageFromUrl = () => {
            if (tempUrl.trim() && imageSourceType === 'url') {
                // Basic URL validation: accept any http(s) URL; still warn if not starting with http(s)
                const urlTrimmed = tempUrl.trim();
                if (!/^https?:\/\//i.test(urlTrimmed)) {
                    setErrorMsg("Invalid image URL. Must start with http:// or https://");
                    return;
                }
                setCustomFaceImageUrl(tempUrl.trim());
                setErrorMsg('');
            }
        };
    
        const handleRemoveFaceImage = () => {
            setCustomFaceImageUrl('');
            setTempUrl('');
            if (fileInputRef.current) {
                fileInputRef.current.value = null; // Clear the file input
            }
            setErrorMsg('');
            
            // Show success notification for face image deletion
            if (onShowSuccessToast) {
                onShowSuccessToast('Face image removed successfully!', 'success');
            }
        };
        
        const handleUrlInputChange = (e) => {
            setTempUrl(e.target.value);
        };
    
        const tooltipTextSection = "Replace AI face: Upload your headshot or paste an image URL (JPEG/PNG, 2MB max).";
    
        return React.createElement('div', { className: 'face-upload-section custom-face-image-block py-3 px-3 rounded-lg border border-gray-700 bg-gray-800/50 mb-3', id: 'custom-face-image-block' },
            React.createElement('div', { className: 'flex items-center justify-between mb-3' },
                React.createElement('div', { className: 'flex items-center gap-2' },
                    React.createElement('label', { className: 'text-sm font-medium text-gray-300', id: 'custom-face-image-label' }, 'Custom Face Upload'),
                    React.createElement('span', { className: 'beta-label text-xs px-1.5 py-0.5 rounded bg-gray-700 text-gray-400 font-medium', id: 'custom-face-image-beta' }, 'beta')
                ),
                React.createElement(TooltipIcon, { tooltipText: tooltipTextSection, tooltipId: 'tooltip-face-source' })
            ),
            // Radio buttons for source type
            React.createElement('div', { className: 'flex items-center gap-6 mb-4', id: 'custom-face-image-source-type-row' },
                React.createElement('label', { htmlFor: 'sourceTypeUpload', className: 'flex items-center cursor-pointer text-sm text-gray-300', id: 'custom-face-image-upload-label' },
                    React.createElement('input', {
                        type: 'radio',
                        id: 'sourceTypeUpload',
                        name: 'imageSourceType',
                        value: 'upload',
                        checked: imageSourceType === 'upload',
                        onChange: () => handleImageSourceTypeChange('upload'),
                        className: 'form-radio h-4 w-4 text-purple-600 bg-gray-700 border-gray-600 focus:ring-purple-500 cursor-pointer mr-2',
                        'aria-labelledby': 'custom-face-image-upload-label'
                    }),
                    React.createElement('span', { className: 'ml-1' }, '🖼 Upload from Device'),
                    React.createElement(TooltipIcon, { tooltipText: "Upload an image file (JPEG, PNG, max 2MB) from your device to use as a custom face for the person in the thumbnail.", tooltipId: "tooltip-face-upload-device" })
                ),
                React.createElement('label', { htmlFor: 'sourceTypeUrl', className: 'flex items-center cursor-pointer text-sm text-gray-300', id: 'custom-face-image-url-label' },
                    React.createElement('input', {
                        type: 'radio',
                        id: 'sourceTypeUrl',
                        name: 'imageSourceType',
                        value: 'url',
                        checked: imageSourceType === 'url',
                        onChange: () => handleImageSourceTypeChange('url'),
                        className: 'form-radio h-4 w-4 text-purple-600 bg-gray-700 border-gray-600 focus:ring-purple-500 cursor-pointer mr-2',
                        'aria-labelledby': 'custom-face-image-url-label'
                    }),
                    React.createElement('span', { className: 'ml-1' }, '🌐 Import via URL'),
                    React.createElement(TooltipIcon, { tooltipText: "Paste an image URL (must be a direct link to a JPG or PNG image, starting with http:// or https://) to use as a custom face.", tooltipId: "tooltip-face-import-url" })
                )
            ),
            // Conditional input based on imageSourceType
            imageSourceType === 'upload' && React.createElement('div', { className: 'flex flex-col gap-3 mb-3', id: 'custom-face-image-upload-input-row' },
                React.createElement('input', {
                    type: 'file',
                    id: 'customFaceFileInput',
                    ref: fileInputRef, // Assign ref
                    accept: '.jpg, .jpeg, .png',
                    onChange: handleFileUpload, // This is passed from App component
                    className: 'block w-full text-sm text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-purple-600 file:text-white hover:file:bg-purple-700 cursor-pointer focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900',
                    'aria-label': 'Upload custom face image file'
                }),
                React.createElement('p', {className: 'text-xs text-gray-500', id: 'custom-face-image-upload-hint'}, "Max 2MB. JPEG or PNG.")
            ),
            imageSourceType === 'url' && React.createElement('div', { className: 'flex items-center gap-2 mb-3', id: 'custom-face-image-url-input-row' },
                React.createElement('input', {
                    type: 'text',
                    id: 'customFaceUrlInput',
                    placeholder: 'Paste image URL here... (e.g., https://example.com/face.jpg)',
                    value: tempUrl,
                    onChange: handleUrlInputChange,
                    onBlur: handleSetFaceImageFromUrl, // Validate and set on blur
                    className: 'flex-grow p-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm focus:ring-purple-500 focus:border-purple-500 text-gray-100 text-sm',
                    'aria-label': 'Custom face image URL input'
                }),
                React.createElement('button', {
                    type: 'button',
                    onClick: handleSetFaceImageFromUrl,
                    className: 'px-3 py-2 text-xs bg-purple-600 hover:bg-purple-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500',
                    id: 'custom-face-image-url-set-btn',
                    'aria-label': 'Set custom face image URL'
                }, 'Set URL')
            ),
            // Preview and Remove button (common for both sources)
            customFaceImageUrl && React.createElement('div', { className: 'flex flex-col items-center gap-3 p-3 border border-gray-700 rounded-md bg-gray-700/30', id: 'custom-face-image-preview-block' },
                 React.createElement('p', { className: 'text-xs text-gray-400 self-start', id: 'custom-face-image-preview-label' }, 'Face Preview:'),
                React.createElement('img', { 
                    src: customFaceImageUrl, 
                    alt: 'Custom Face Preview', 
                    className: 'w-20 h-20 rounded-full object-cover border-2 border-purple-500 shadow-md',
                    id: 'custom-face-image-preview-img',
                    onError: (e) => { 
                        e.target.style.display='none'; // Hide img tag on error
                        if (!document.getElementById('img-error-msg')){
                            const errorP = document.createElement('p');
                            errorP.id = 'img-error-msg';
                            errorP.className = 'text-xs text-red-400';
                            errorP.textContent = 'Preview unavailable. Check URL or file.';
                            e.target.parentNode.appendChild(errorP);
                        }
                    },
                    onLoad: (e) => {
                        const errorMsgEl = document.getElementById('img-error-msg');
                        if (errorMsgEl) errorMsgEl.remove();
                        e.target.style.display='';
                    }
                }),
                React.createElement('button', {
                    type: 'button',
                    onClick: handleRemoveFaceImage,
                    className: 'custom-face-image-remove-btn w-6 h-6 bg-red-600 rounded-full flex items-center justify-center opacity-100 transition-all duration-200 cursor-pointer hover:bg-red-500 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2 focus:ring-offset-gray-800',
                    id: 'custom-face-image-remove-btn',
                    'aria-label': 'Remove custom face image',
                    title: 'Remove image'
                },
                    React.createElement('span', {
                        className: 'iconify',
                        'data-icon': 'solar:trash-bin-minimalistic-linear',
                        style: { fontSize: '16px', color: 'white' }
                    })
                )
            ),
            // Disclaimer at the bottom
            React.createElement('p', { className: 'text-xs text-gray-500 mt-2 italic', id: 'custom-face-image-disclaimer' },
                "This feature is currently under development. Results may not be 100% accurate and could contain mistakes."
            )
        );
    };

    // Create Expression Picker
    const createMoodAndExpressionPicker = () => {
        if (!includePerson) return null;
    
        const expressionOptions = [
            { emoji: '🙂', label: 'Default', value: 'Default' },
            { emoji: '😊', label: 'Happy', value: 'Happy' },
            { emoji: '😳', label: 'Shocked', value: 'Shocked' },
            { emoji: '😍', label: 'Loved', value: 'Loved' },
            { emoji: '🤔', label: 'Thinking', value: 'Thinking' },
            { emoji: '😠', label: 'Angry', value: 'Angry' },
            { emoji: '😢', label: 'Crying', value: 'Crying' },
            { emoji: '😆', label: 'Laughing', value: 'Laughing' },
            { emoji: '😐', label: 'Neutral', value: 'Neutral' },
            { emoji: '😎', label: 'Proud', value: 'Proud' }
        ];
    
        const handleExpressionClick = (newValue) => {
            setSelectedExpression(newValue);
        };
    
        const handleExpressionKeyDown = (e, newValue) => {
            if (e.key === 'Enter' || e.key === ' ') {
                setSelectedExpression(newValue);
                e.preventDefault();
            }
        };
    
        const buttons = expressionOptions.map(option =>
            React.createElement('button', {
                key: option.value,
                type: 'button',
                onClick: () => handleExpressionClick(option.value),
                onKeyDown: (e) => handleExpressionKeyDown(e, option.value),
                title: option.label,
                className: `flex flex-col items-center justify-center p-2 rounded-lg border-2 text-center ${selectedExpression === option.value ? 'border-purple-500 bg-purple-700' : 'border-gray-600 bg-gray-700 hover:border-purple-400'} transition-all focus:outline-none focus:ring-2 focus:ring-purple-400 w-full`,
                id: `expression-btn-${option.value.toLowerCase().replace(/\s+/g, '-')}`,
                'aria-label': `${option.emoji} ${option.label}`,
                'aria-pressed': selectedExpression === option.value
            },
                React.createElement('span', { 'aria-hidden': 'true', className: 'text-3xl' }, option.emoji),
                React.createElement('span', { className: 'block text-xs mt-1 text-gray-300' }, option.label)
            )
        );
    
        return React.createElement('div', { className: 'mood-expression-picker-section py-2 overflow-visible', id: 'mood-expression-picker-section' },
            React.createElement('label', { className: 'mood-expression-picker-label block text-sm font-medium text-gray-300 mb-2' }, 'Mood & Expression:'),
            React.createElement('div', { className: 'mood-expression-picker-grid grid grid-cols-4 sm:grid-cols-4 gap-2 px-6 py-3 -mx-2 overflow-visible' }, buttons)
        );
    };

    // Create Gender Selector
    const createGenderSelector = () => {
        if (!includePerson) return null;
        const genderOptions = [
            { emoji: '🤖', label: 'Auto', value: 'Auto' },
            { emoji: '👨', label: 'Male', value: 'Male' },
            { emoji: '👩', label: 'Female', value: 'Female' },
            { emoji: '🧑', label: 'Non-binary', value: 'Non-binary' }
        ];
        const tooltipText = "Specify the gender of the person in the thumbnail. 'Auto' lets the AI decide. This is only active if 'Include Person' is ON.";
    
        return React.createElement('div', { className: 'gender-selector-section py-2', id: 'gender-selector-section' },
            React.createElement('div', { className: 'flex items-center justify-between mb-1'},
                React.createElement('label', { htmlFor: 'genderSelect', className: 'text-sm font-medium text-gray-300' }, 'Preferred Gender:'),
                React.createElement(TooltipIcon, { tooltipText: tooltipText, tooltipId: 'tooltip-gender' })
            ),
            React.createElement('div', { className: 'grid grid-cols-4 gap-2 mt-2', role: 'radiogroup', 'aria-label': 'Gender selection' },
                genderOptions.map(gender => React.createElement('button', {
                    key: gender.value,
                    onClick: () => setSelectedGender(gender.value),
                    className: `gender-card flex flex-col items-center justify-center p-2 ${selectedGender === gender.value ? 'bg-purple-700 border-purple-500' : 'bg-gray-700 border-gray-600'} border rounded-md shadow-sm hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50`,
                    'aria-pressed': selectedGender === gender.value,
                    'aria-label': gender.label,
                    disabled: !includePerson
                },
                    React.createElement('span', { className: 'text-2xl mb-1' }, gender.emoji),
                    React.createElement('span', { className: 'text-xs font-medium' }, gender.label)
                ))
            )
        );
    };

    // Create text overlay text editor
    const createTextOverlayEditor = () => {
        if (!textOverlay) return null;
    
        // Show current overlay text or placeholder - since overlay text is now auto-populated, this should always have content
        const placeholder = 'YOUR VIDEO\nTITLE\nHERE';
        
        // Auto-uppercase text handler
        const handleOverlayTextChange = (e) => {
            const inputValue = e.target.value;
            const uppercaseValue = inputValue.toUpperCase();
            setOverlayText(uppercaseValue);
        };
        
        return React.createElement('div', { 
            className: 'text-overlay-editor-section py-2',
            id: 'text-overlay-editor-section'
        },
            React.createElement('div', { 
                className: 'text-overlay-header flex items-center justify-between mb-2',
                id: 'text-overlay-header'
            },
                React.createElement('label', { 
                    className: 'glass-label text-overlay-label block',
                    id: 'text-overlay-label'
                }, 'Overlay Text:'),
                React.createElement('div', {
                    className: 'text-overlay-action-buttons flex items-center gap-1',
                    id: 'text-overlay-action-buttons'
                },
                    // Refresh Text Overlay Button (with tooltip)
                    React.createElement('div', {
                        className: 'relative group',
                        id: 'refreshTextOverlayButtonContainer',
                        style: { minWidth: '28px', minHeight: '28px' }
                    },
                        React.createElement('button', {
                            type: 'button',
                            id: 'text-overlay-refresh-btn',
                            onClick: async () => {
                                // Prevent multiple simultaneous refresh requests
                                if (isRefreshingOverlayText) {
                                    return;
                                }

                                if (!userPrompt || userPrompt.trim().length < 3) {
                                    setErrorMsg("Please enter a video topic first to generate overlay text!");
                                    return;
                                }

                                setIsRefreshingOverlayText(true);
                                setErrorMsg(''); // Clear any existing errors

                                try {
                                    // Generate new UNIQUE text overlay suggestion with enhanced variation system
                                    const { generateSmartTextSuggestion } = await import('../utils/smartTextAnalyzer.js');
                                    
                                    // Force generation of new suggestion (not from cache)
                                    const newSuggestion = await generateSmartTextSuggestion(userPrompt || '', 'general', true);
                                    
                                    if (newSuggestion && newSuggestion.trim() !== '') {
                                        // Apply sanitization to ensure safe characters (already uppercase from generator)
                                        const { sanitizeOverlayText } = await import('../utils/textSanitizer.js');
                                        const sanitizedSuggestion = sanitizeOverlayText(newSuggestion, {
                                            preserveSpaces: false,
                                            removeEmojis: true,
                                            removeNumbers: false,
                                            convertToUppercase: true
                                        });
                                        setOverlayText(sanitizedSuggestion);
                                        
                                        // Debug logging for variation tracking
                                        console.log(`[Text Overlay Refresh] Generated unique suggestion: "${sanitizedSuggestion}" (${sanitizedSuggestion.split(' ').length} words)`);
                                    } else {
                                        throw new Error('No suggestion generated');
                                    }
                                } catch (error) {
                                    console.error('Failed to generate new text overlay suggestion:', error);
                                    
                                    // Enhanced Fallback: Use the enhanced fallback system for unique suggestions
                                    if (userPrompt && userPrompt.trim() !== '') {
                                        try {
                                            const { generateFallbackSuggestion } = await import('../utils/smartTextAnalyzer.js');
                                            const fallbackText = generateFallbackSuggestion(userPrompt);
                                            
                                            // Apply sanitization to fallback as well
                                            const { sanitizeOverlayText } = await import('../utils/textSanitizer.js');
                                            const sanitizedFallback = sanitizeOverlayText(fallbackText, {
                                                preserveSpaces: false,
                                                removeEmojis: true,
                                                removeNumbers: false,
                                                convertToUppercase: true
                                            });
                                            setOverlayText(sanitizedFallback);
                                            
                                            console.log(`[Text Overlay Refresh] Using enhanced fallback: "${sanitizedFallback}"`);
                                        } catch (fallbackError) {
                                            console.error('Enhanced fallback also failed:', fallbackError);
                                            // Final simple fallback
                                            const words = userPrompt.toUpperCase().split(/[\s:!?.,;]+/);
                                            const keyWords = words.filter(word => 
                                                word.length > 3 && 
                                                !['THE', 'AND', 'WITH', 'FOR', 'YOUR', 'THIS', 'THAT'].includes(word)
                                            ).slice(0, 2);
                                            const finalFallback = keyWords.length > 0 ? keyWords.join(' ') + '!' : 'AMAZING!';
                                            setOverlayText(finalFallback);
                                        }
                                    } else {
                                        setErrorMsg("Could not generate text overlay. Please enter a video topic first.");
                                    }
                                } finally {
                                    // Reset loading state
                                    setIsRefreshingOverlayText(false);
                                }
                            },
                            className: `text-overlay-refresh-button w-full h-full rounded-full transition-colors duration-200 backdrop-filter backdrop-blur-[8px] shadow-[0_2px_8px_rgba(0,0,0,0.15)] flex items-center justify-center ${isRefreshingOverlayText ? 'bg-purple-600/80 border-purple-500/70 text-white' : 'bg-gray-700/90 border border-gray-600 hover:border-blue-500/50 text-gray-400 hover:text-purple-300'} focus:outline-none hover:bg-purple-500/10`,
                            style: {
                                width: '28px',
                                height: '28px',
                                borderRadius: '50%',
                                minWidth: '28px',
                                minHeight: '28px',
                                padding: '0',
                                fontSize: '0.875rem'
                            },
                            'aria-label': isRefreshingOverlayText ? 'Generating text overlay suggestion' : 'Refresh text overlay suggestion',
                            disabled: isRefreshingOverlayText || !userPrompt || userPrompt.trim().length < 3
                        },
                            React.createElement('span', {
                                className: 'iconify',
                                'data-icon': 'solar:refresh-square-linear',
                                style: {
                                    fontSize: '1rem',
                                    width: 'auto',
                                    height: 'auto'
                                }
                            })
                        ),
                        // Compact Tooltip
                        React.createElement('div', {
                            className: 'absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-150 delay-100 pointer-events-none z-20 shadow-lg border border-gray-700 text-overlay-tooltip-compact',
                            id: 'refreshTextOverlayTooltip',
                            style: {
                                transform: 'translateX(-65%)', // Shift left by additional 15% to prevent edge cutting
                                marginLeft: '-35px' // Additional left offset for better positioning
                            }
                        }, isRefreshingOverlayText ? 'Generating...' : 'Refresh suggestion')
                    ),
                    
                    // Edit Button (with tooltip)
                    React.createElement('div', {
                        className: 'relative group',
                        id: 'editTextOverlayButtonContainer'
                    },
                        React.createElement('button', {
                            type: 'button',
                            id: 'text-overlay-edit-toggle-btn',
                            onClick: () => setIsEditingOverlayText(prev => !prev),
                            className: `text-overlay-edit-toggle px-2 py-1 rounded transition-all border-none bg-transparent flex items-center gap-1`,
                            style: {
                                fontSize: '0.8rem', // 10% larger than text-xs (0.75rem)
                                fontWeight: '500',
                                color: isEditingOverlayText ? '#a855f7' : '#9ca3af', // purple-500 : gray-400
                                textDecoration: 'none',
                                cursor: 'pointer',
                                transition: 'color 0.25s cubic-bezier(0.4, 0, 0.2, 1), text-decoration 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
                                opacity: 1
                            },
                            onMouseEnter: (e) => {
                                const button = e.currentTarget;
                                // Always check current editing state from DOM or component state
                                const currentEditingState = button.getAttribute('aria-label')?.includes('Close');
                                button.style.color = '#ffffff'; // white on hover
                                button.style.textDecoration = 'underline';
                                // Removed translateY movement for fade-only transition
                                // Icon transform effects removed for cleaner interaction
                            },
                            onMouseLeave: (e) => {
                                const button = e.currentTarget;
                                // Always check current editing state from DOM or component state
                                const currentEditingState = button.getAttribute('aria-label')?.includes('Close');
                                button.style.color = currentEditingState ? '#a855f7' : '#9ca3af';
                                button.style.textDecoration = 'none';
                                // Removed translateY movement for fade-only transition
                                // Icon transform effects removed for cleaner interaction
                            },
                            'aria-label': isEditingOverlayText ? 'Close Text Editor' : 'Edit Overlay Text'
                        },
                            // Icon - always render with consistent pen icon
                            React.createElement('span', {
                                key: 'edit-icon', // Add key for React reconciliation
                                className: 'edit-icon iconify',
                                'data-icon': 'solar:pen-linear',
                                style: {
                                    fontSize: '1.1em',
                                    transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
                                    opacity: 1,
                                    transform: 'rotate(0deg) scale(1)',
                                    display: 'inline-flex',
                                    alignItems: 'center',
                                    width: 'auto',
                                    overflow: 'visible'
                                }
                            }),
                            // Text label with fade transition
                            React.createElement('span', {
                                key: 'edit-text', // Add key for React reconciliation
                                style: {
                                    transition: 'opacity 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
                                    opacity: 1
                                }
                            }, isEditingOverlayText ? 'Close' : 'Edit')
                        ),
                        // Tooltip
                        React.createElement('div', {
                            className: 'absolute bottom-full mb-2 right-0 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-150 delay-100 pointer-events-none z-20 shadow-lg border border-gray-700 text-overlay-tooltip-compact',
                            id: 'editTextOverlayTooltip'
                        }, isEditingOverlayText ? 'Close editor' : 'Edit overlay text')
                    )
                )
            ),
            

            
            isEditingOverlayText && React.createElement('div', { 
                className: 'text-overlay-editor-container mt-1',
                id: 'text-overlay-editor-container',
                style: {
                    animation: 'textareaSlideIn 0.35s cubic-bezier(0.4, 0, 0.2, 1) forwards',
                    opacity: 0,
                    transform: 'translateY(-12px)'
                }
            },
                React.createElement('textarea', {
                    id: 'text-overlay-textarea',
                    className: 'glass-input text-overlay-textarea resize-none',
                    rows: '3',
                    placeholder: placeholder,
                    value: overlayText || '', // Ensure controlled component
                    onChange: handleOverlayTextChange, // Use auto-uppercase handler
                    'aria-label': 'Edit overlay text (automatically converted to uppercase)',
                    autoComplete: 'off',
                    style: {
                        transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
                        textTransform: 'uppercase' // Visual feedback in textarea
                    }
                }),
                React.createElement('p', { 
                    className: 'text-overlay-tip text-xs text-gray-400 mt-1',
                    id: 'text-overlay-tip',
                    style: {
                        transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)'
                    }
                }, 'Text automatically converted to UPPERCASE • Pyramid shape recommended')
            ),
            !isEditingOverlayText && React.createElement('div', { 
                className: `text-overlay-preview mt-2 text-sm overflow-hidden h-20 bg-gray-700/30 backdrop-filter backdrop-blur-sm border border-gray-600/50 rounded-lg relative ${isRefreshingOverlayText ? 'overlay-text-loading' : ''}`,
                id: 'text-overlay-preview',
                style: {
                    background: 'linear-gradient(135deg, rgba(255,255,255,0.03) 0%, rgba(255,255,255,0.01) 100%)',
                    borderRadius: '12px',
                    padding: '12px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    gap: '2px',
                    minHeight: '80px'
                }
            },
                // Loading overlay with blur and spinner
                isRefreshingOverlayText && React.createElement('div', {
                    className: 'absolute inset-0 bg-gray-900/80 backdrop-blur-sm rounded-lg flex items-center justify-center',
                    style: {
                        zIndex: 10,
                        backdropFilter: 'blur(4px)',
                        WebkitBackdropFilter: 'blur(4px)'
                    }
                },
                    React.createElement('div', {
                        className: 'w-10 h-10 border-4 border-gray-600/30 border-t-white rounded-full animate-spin'
                    })
                ),
                // Show actual overlay text if available, otherwise show placeholder
                (overlayText || placeholder).split('\n').map((line, i) => (
                    React.createElement('div', { 
                        key: `preview-line-${i}`, 
                        className: `text-overlay-preview-line ${overlayText ? 'text-gray-100' : 'text-gray-400'}`,
                        id: `text-overlay-preview-line-${i}`,
                        style: {
                            fontWeight: overlayText ? '600' : '400',
                            fontSize: `${Math.max(0.75, 1 - (i * 0.1))}rem`,
                            lineHeight: '1.2',
                            textAlign: 'center',
                            letterSpacing: '0.5px',
                            textTransform: 'uppercase',
                            fontFamily: overlayText ? "'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" : "'Geist Mono', 'JetBrains Mono', 'Fira Code', monospace",
                            opacity: overlayText ? (i === 0 ? 1 : 0.9 - (i * 0.1)) : 0.6,
                            transition: 'all 0.2s ease'
                        }
                    }, line || ' ')
                ))
            )
        );
    };

    // Create text position selector
    const createTextPositionSelector = () => {
        if (!textOverlay) return null;
    
        const positions = [
            "Top Left", "Top Center", "Top Right",
            "Center",
            "Bottom Left", "Bottom Center", "Bottom Right"
        ];
    
        return React.createElement('div', { 
            className: 'text-position-selector-section py-2',
            id: 'text-position-selector-section'
        },
            React.createElement('label', { 
                htmlFor: 'text-position-select-dropdown',
                className: 'glass-label text-position-label block mb-1',
                id: 'text-position-label'
            }, 'Text Position:'),
            React.createElement('div', { className: 'glass-select-wrapper' },
                React.createElement('select', {
                    id: 'text-position-select-dropdown',
                    value: textPosition,
                    onChange: (e) => setTextPosition(e.target.value),
                    className: 'glass-select text-position-select-input'
                }, positions.map(pos => React.createElement('option', { 
                    key: pos, 
                    value: pos,
                    id: `text-position-option-${pos.toLowerCase().replace(/\s+/g, '-')}`
                }, pos))),
                React.createElement('span', {
                    className: 'glass-select-chevron iconify',
                    'data-icon': 'solar:alt-arrow-down-linear'
                })
            )
        );
    };

    // Create color palette and manual color pickers for text
    const createTextColorPickers = () => {
        if (!textOverlay) return null;
        
        // Inline color utilities (moved outside of useMemo to fix hooks error)
        // Using imported CORE_COLORS and CORE_COLOR_PALETTE from colorUtils.js

        const hexToHsl = (hex) => {
            hex = hex.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16) / 255;
            const g = parseInt(hex.substr(2, 2), 16) / 255;
            const b = parseInt(hex.substr(4, 2), 16) / 255;
            const max = Math.max(r, g, b);
            const min = Math.min(r, g, b);
            let h, s, l = (max + min) / 2;
            if (max === min) {
                h = s = 0;
            } else {
                const d = max - min;
                s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                switch (max) {
                    case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                    case g: h = (b - r) / d + 2; break;
                    case b: h = (r - g) / d + 4; break;
                }
                h /= 6;
            }
            return { h: h * 360, s: s * 100, l: l * 100 };
        };

        const hslToHex = (h, s, l) => {
            h = h / 360; s = s / 100; l = l / 100;
            const hue2rgb = (p, q, t) => {
                if (t < 0) t += 1;
                if (t > 1) t -= 1;
                if (t < 1/6) return p + (q - p) * 6 * t;
                if (t < 1/2) return q;
                if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                return p;
            };
            let r, g, b;
            if (s === 0) {
                r = g = b = l;
            } else {
                const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
                const p = 2 * l - q;
                r = hue2rgb(p, q, h + 1/3);
                g = hue2rgb(p, q, h);
                b = hue2rgb(p, q, h - 1/3);
            }
            const toHex = (c) => {
                const hex = Math.round(c * 255).toString(16);
                return hex.length === 1 ? '0' + hex : hex;
            };
            return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
        };

        // Using imported generateDarkerAccentShade function from colorUtils.js

        const generateTextGradient = (primaryColor, secondaryColor = null) => {
            const accentColor = secondaryColor || generateDarkerAccentShade(primaryColor);
            return `linear-gradient(135deg, ${primaryColor} 0%, ${accentColor} 100%)`;
        };

        const getColorCircleStyles = (isSelected) => {
            const baseStyles = {
                transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
                willChange: 'transform, box-shadow',
                transform: 'scale(1)',
                boxShadow: 'none'
            };
            if (isSelected) {
                return {
                    ...baseStyles,
                    transform: 'scale(1.1)',
                    boxShadow: '0 0 0 2px rgba(255, 255, 255, 0.9), 0 4px 12px rgba(0, 0, 0, 0.4)'
                };
            }
            return baseStyles;
        };

        // Using imported isCoreColor function from colorUtils.js

        // Tooltip text for color section
        const tooltipTextSection = "Pick a primary color. We'll automatically create a darker gradient accent for enhanced contrast.";
        
        return React.createElement('div', { 
            className: 'text-color-typography-control-section py-4 mt-4 border-t border-gray-700', 
            id: 'text-color-typography-control-section' 
        },
            // Section header with icon and tooltip
            React.createElement('div', { 
                className: 'text-color-control-header flex items-center gap-2 mb-4',
                id: 'text-color-control-header'
            },
                React.createElement('span', { 
                    className: 'text-purple-400 text-base',
                    id: 'text-color-control-section-icon',
                    dangerouslySetInnerHTML: { __html: '<i class="solar solar-palette-bold-duotone"></i>' }
                }),
                React.createElement('h4', { 
                    className: 'text-sm font-semibold text-white',
                    id: 'text-color-control-section-title'
                }, 'Text Color & Typography'),
                React.createElement(TooltipIcon, { tooltipText: tooltipTextSection, tooltipId: 'tooltip-text-color' })
            ),
            
            // Primary Accent Color Label
            React.createElement('div', { className: 'mb-4' },
                React.createElement('label', { className: 'glass-label block mb-3' }, 'Primary Accent Color:')
            ),
            
            // Enhanced Color Picker with Glass Styling
            React.createElement('div', { 
                className: 'glass-color-picker enhanced-color-picker-container',
                id: 'enhanced-color-picker-container'
            },
                React.createElement('div', { 
                    className: 'color-palette-circles'
                },
                    CORE_COLOR_PALETTE.map((colorItem, index) => {
                        const isActive = primaryTextColor.toLowerCase() === colorItem.color.toLowerCase();
                        const accentColor = generateDarkerAccentShade(colorItem.color);
                    
                        return React.createElement('button', {
                        key: index,
                            className: `color-circle ${isActive ? 'active' : ''}`,
                            style: { 
                                background: `linear-gradient(135deg, ${colorItem.color}, ${accentColor})`,
                                // Special border for black color visibility
                                border: colorItem.color === '#000000' ? '1px solid rgba(255, 255, 255, 0.3)' : 'none'
                            },
                            onClick: (e) => {
                                // Immediate state update for responsive feedback
                                e.preventDefault();
                                const newSecondary = generateDarkerAccentShade(colorItem.color);
                                setPrimaryTextColor(colorItem.color);
                                setSecondaryTextColor(newSecondary);
                            },
                            onMouseDown: (e) => {
                                // Prevent focus ring on mouse click
                                e.preventDefault();
                            },
                            onKeyDown: (e) => {
                                // Handle keyboard navigation
                                if (e.key === 'Enter' || e.key === ' ') {
                                    e.preventDefault();
                                    const newSecondary = generateDarkerAccentShade(colorItem.color);
                                    setPrimaryTextColor(colorItem.color);
                                    setSecondaryTextColor(newSecondary);
                                }
                            },
                            title: `${colorItem.name} Gradient`,
                            'aria-label': `Select ${colorItem.name} gradient color`,
                            'aria-pressed': isActive,
                            tabIndex: 0,
                            role: 'button'
                        });
                })
                )
            ),
            
            // Current Color Display with Auto-Generated Gradient (Hidden)
                React.createElement('div', {
                className: 'current-color-display flex items-center gap-3 mt-3 p-3 bg-gray-700/50 rounded-lg border border-gray-600',
                id: 'current-color-display',
                style: { display: 'none' }
            },
                React.createElement('div', { className: 'color-preview-circles flex items-center gap-2' },
                    React.createElement('div', {
                        className: 'primary-color-circle w-6 h-6 rounded-full border-2 border-gray-400',
                        style: { backgroundColor: primaryTextColor },
                        title: `Primary: ${primaryTextColor.toUpperCase()}`
                }),
                    React.createElement('span', { className: 'text-gray-400 text-sm' }, '→'),
                    React.createElement('div', {
                        className: 'secondary-color-circle w-6 h-6 rounded-full border-2 border-gray-400',
                        style: { backgroundColor: secondaryTextColor },
                        title: `Accent: ${secondaryTextColor.toUpperCase()}`
                    })
            ),
                React.createElement('div', { className: 'color-info flex flex-col' },
                    React.createElement('span', { className: 'text-sm font-medium text-white' }, 
                        `${CORE_COLOR_PALETTE.find(c => c.color.toUpperCase() === primaryTextColor.toUpperCase())?.name || 'Custom'} Gradient`
                    ),
                    React.createElement('span', { className: 'text-xs text-gray-400' }, 
                        `${primaryTextColor.toUpperCase()} → ${secondaryTextColor.toUpperCase()}`
                    )
                )
            ),
            
            // Live preview with gradient text
            React.createElement('div', { 
                className: 'text-style-live-preview-container rounded-md border border-gray-700 bg-gray-900 p-4 flex flex-col items-center mt-4 overflow-hidden',
                id: 'text-style-live-preview-container',
                'aria-label': 'Text style live preview'
            },
                React.createElement('div', { 
                    className: 'text-style-preview-header w-full flex items-center justify-between mb-1',
                    id: 'text-style-preview-header'
                },
                    React.createElement('span', { 
                        className: 'text-style-preview-label text-xs text-gray-400 font-medium',
                        id: 'text-style-preview-label'
                    }, 'Live Preview:'),
                    React.createElement('span', { 
                        className: 'text-style-preview-specs text-xs text-gray-500 font-mono',
                        id: 'text-style-preview-specs'
                    }, 
                        `${selectedFontFamily} / ${selectedTextSize}`
                    )
                ),
                React.createElement('div', {
                    className: 'text-style-preview-display-area w-full rounded overflow-hidden p-3 flex flex-col items-center justify-center text-center',
                    id: 'text-style-preview-display-area',
                    style: {
                        background: 'linear-gradient(135deg, #1a1a2e 0%, #222244 100%)',
                        minHeight: '80px'
                    }
                },
                    (overlayText || 'YOUR VIDEO TITLE')
                        .toUpperCase()
                        .split('\n')
                        .map((line, idx) => {
                            const gradientStyle = generateTextGradient(primaryTextColor, secondaryTextColor);
                            return React.createElement('span', {
                                key: idx,
                                id: `text-style-preview-line-${idx}`,
                                className: 'text-style-preview-gradient-line',
                                style: {
                                    fontFamily: selectedFontFamily,
                                    fontSize: selectedTextSize === 'Large' ? '1.8rem' : selectedTextSize === 'Medium' ? '1.4rem' : '1rem',
                                    fontWeight: 'bold',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.02em',
                                    lineHeight: '1.1',
                                    marginBottom: '0.2em',
                                    background: gradientStyle,
                                    WebkitBackgroundClip: 'text',
                                    WebkitTextFillColor: 'transparent',
                                    backgroundClip: 'text',
                                    color: 'transparent',
                                    textShadow: 'none', // Remove text shadow for gradient text
                                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
                                }
                            }, line);
                        })
                )
            )
        );
    };

    // Create Icon Rendering Mode Selector
    const createIconRenderingModeSelector = () => {
        if (!includeIcons) return null;
        
        const modes = [
            { 
                id: 'auto', 
                name: 'Smart Auto', 
                description: 'Automatically choose realistic or cartoonish\nbased on object type',
                icon: 'solar:check-circle-linear'
            },
            { 
                id: 'realistic', 
                name: 'All Realistic', 
                description: 'Render all icons with 70-80% photorealistic\nquality',
                icon: 'solar:camera-linear'
            },
            { 
                id: 'cartoonish', 
                name: 'All Cartoonish', 
                description: 'Render all icons in 3D cartoonish style',
                icon: 'solar:emoji-funny-square-linear'
            }
        ];

        const tooltipText = "Choose how icons should be rendered: Smart Auto classifies objects automatically, Realistic makes all icons photorealistic, Cartoonish makes all icons stylized.";

        return React.createElement('div', { 
            className: 'icon-rendering-mode-section', 
            id: 'icon-rendering-mode-section' 
        },
            // Section label with tooltip
            React.createElement('div', { 
                className: 'icon-rendering-header flex items-center justify-between mb-3',
                id: 'icon-rendering-header'
            },
                React.createElement('label', { 
                    className: 'icon-rendering-section-title text-sm font-normal text-white',
                    id: 'icon-rendering-section-title'
                }, 'Icon Rendering Type'),
                React.createElement(TooltipIcon, { tooltipText: tooltipText, tooltipId: 'tooltip-icon-rendering' })
            ),
            
            // Mode selection list
            React.createElement('div', { 
                className: 'icon-rendering-mode-list flex flex-col gap-3', 
                id: 'icon-rendering-mode-list',
                role: 'radiogroup', 
                'aria-label': 'Icon rendering mode selection' 
            },
                modes.map(mode => {
                    const isSelected = iconRenderingMode === mode.id;
                    return React.createElement('button', {
                        key: mode.id,
                        id: `icon-rendering-mode-${mode.id}-button`,
                        onClick: () => setIconRenderingMode(mode.id),
                        className: `icon-rendering-mode-option relative bg-[rgba(18,24,38,0.2)] hover:bg-[rgba(18,24,38,0.3)] border-0 rounded-[14px] p-3 text-left transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                            isSelected 
                                ? 'bg-[rgba(18,24,38,0.3)] ring-[3px] ring-blue-500 shadow-[0px_20px_30px_-5px_rgba(0,111,238,0.3),0px_10px_20px_-5px_rgba(0,0,0,0.4),0px_0px_0px_1px_rgba(0,111,238,0.2)]' 
                                : 'shadow-[0px_1px_2px_0px_rgba(0,0,0,0.05)]'
                        }`,
                        'aria-pressed': isSelected,
                        'aria-describedby': `icon-rendering-mode-description-${mode.id}`,
                        role: 'radio',
                        'aria-checked': isSelected
                    },
                        React.createElement('div', { 
                            className: 'icon-rendering-mode-content',
                            id: `icon-rendering-mode-content-${mode.id}`
                        },
                            React.createElement('div', { 
                                className: 'icon-rendering-mode-header flex items-center gap-1.5 mb-2',
                                id: `icon-rendering-mode-header-${mode.id}`
                            },
                                React.createElement('span', { 
                                    className: `iconify w-5 h-5 ${isSelected ? 'text-[#fff]' : 'text-zinc-400'}`,
                                    'data-icon': mode.icon,
                                    id: `icon-rendering-mode-icon-${mode.id}`
                                }),
                                React.createElement('span', { 
                                    className: `icon-rendering-mode-name ${isSelected ? 'font-semibold text-white' : 'font-normal text-zinc-400'} text-base leading-6`,
                                    id: `icon-rendering-mode-name-${mode.id}`
                                }, mode.name)
                            ),
                            React.createElement('p', { 
                                className: `icon-rendering-mode-description text-xs leading-4 ${isSelected ? 'text-zinc-400' : 'text-zinc-500'} whitespace-pre-line`,
                                id: `icon-rendering-mode-description-${mode.id}`
                            }, mode.description)
                        ),
                        // Filled checkmark for selected option (positioned in top-right)
                        isSelected && React.createElement('span', { 
                            className: 'icon-rendering-mode-selected-indicator absolute top-3 right-3 text-blue-500',
                            id: `icon-rendering-mode-selected-indicator-${mode.id}`,
                            'aria-label': 'Selected'
                        },
                            React.createElement('span', {
                                className: 'iconify w-5 h-5',
                                'data-icon': 'solar:check-circle-bold'
                            })
                        )
                    );
                })
            )
        );
    };

    // Check if all design controls are OFF for validation banner
    const hasAnyDesignControl = includePerson || includeIcons || textOverlay;
    const isForbiddenState = !hasAnyDesignControl;

    // Ref for the forbidden state banner
    const forbiddenBannerRef = React.useRef(null);
    const scrollableContainerRef = React.useRef(null);

    // Auto-scroll to red banner when it appears
    React.useEffect(() => {
        if (isForbiddenState && forbiddenBannerRef.current && scrollableContainerRef.current) {
            // Small delay to ensure banner is rendered and DOM is updated
            setTimeout(() => {
                const banner = forbiddenBannerRef.current;
                const container = scrollableContainerRef.current;

                if (banner && container) {
                    // Get the banner's position relative to the container
                    const bannerOffsetTop = banner.offsetTop;

                    // Calculate optimal scroll position to align scrollbar with red banner
                    // We want the red banner to be at the top of the visible area
                    const targetScrollTop = Math.max(0, bannerOffsetTop - 10); // 10px padding from top

                    // Smooth scroll to align with red banner
                    container.scrollTo({
                        top: targetScrollTop,
                        behavior: 'smooth'
                    });

                    // Add visual feedback class
                    banner.classList.add('scroll-aligned');
                    setTimeout(() => {
                        if (banner) banner.classList.remove('scroll-aligned');
                    }, 1000);
                }
            }, 150); // Increased delay for better reliability
        }
    }, [isForbiddenState]);

    // Forbidden state banner component
    const forbiddenStateBanner = isForbiddenState ? React.createElement('div', {
        ref: forbiddenBannerRef,
        className: 'forbidden-state-banner bg-red-900/20 border border-red-500/30 rounded-lg p-3 mb-4 scroll-to-red-banner',
        id: 'forbidden-state-banner',
        'aria-live': 'polite',
        role: 'alert'
    },
        React.createElement('div', {
            className: 'flex items-start gap-3'
        },
            React.createElement('span', {
                className: 'iconify text-red-400 flex-shrink-0 mt-0.5',
                'data-icon': 'solar:shield-warning-bold',
                style: { fontSize: '20px' }
            }),
            React.createElement('div', {
                className: 'text-sm'
            },
                React.createElement('div', {
                    className: 'font-semibold text-red-300 mb-1'
                }, 'Design Controls Required'),
                React.createElement('div', {
                    className: 'text-red-200'
                }, 'Please turn on at least one option to continue.')
            )
        )
    ) : null;

    // Group sections based on functionality
    const basicControlsSection = React.createElement('div', {
        className: 'basic-controls-container flex flex-col gap-2',
        id: 'basic-controls-container'
    },
        forbiddenStateBanner,
        createToggle('Enable Text Overlay?', 'toggleText', textOverlay, setTextOverlay, 'Show bold, eye-catching text on your thumbnail. Recommended for most YouTube videos.'),
        createToggle('Include Person?', 'togglePerson', includePerson, setIncludePerson, 'Add a human figure or face to your thumbnail. Faces increase engagement and click-through rates.'),
        createToggle('Include Icons?', 'toggleIcons', includeIcons, setIncludeIcons, 'Add relevant icons, badges, or symbols to visually reinforce your video topic.')
    );

    const textSettingsSection = React.createElement('div', {
        className: 'text-settings-container flex flex-col gap-2',
        id: 'text-settings-container'
    },
        createTextOverlayEditor(),
        createTextPositionSelector(),
        createTextColorPickers(),
        createTextSizeSelector()
    );

    // Create a wrapper function for the tooltip icon that child components can call
    const createTooltipIcon = (tooltipText, tooltipId = '') => {
        return React.createElement(TooltipIcon, { tooltipText, tooltipId });
    };

    // Define person settings content using new components
    const personSettingsContent = React.createElement('div', { 
        className: 'person-settings-container flex flex-col gap-2 overflow-visible',
        id: 'person-settings-container'
    },
        React.createElement(MoodAndExpressionPicker, { selectedExpression, setSelectedExpression }),
        React.createElement(GenderSelector, { selectedGender, setSelectedGender, createTooltipIconProp: createTooltipIcon }),
        React.createElement(FaceUploadSection, {
            imageSourceType,
            customFaceImageUrl,
            handleImageSourceTypeChange,
            setCustomFaceImageUrl,
            handleFileUpload,
            setErrorMsg,
            createTooltipIconProp: createTooltipIcon,
            openImageRequirementsModal,
            onShowSuccessToast
        })
    );

    // Render the panel with collapsible sections and glass styling
    return (
        React.createElement('div', { 
            className: 'design-controls-glass-container p-4 flex flex-col gap-4 overflow-y-auto overflow-x-visible max-h-[calc(100vh-10rem)] min-h-0',
            id: 'control-panel-main-container'
        },
            // Glass-styled heading
            React.createElement('h2', {
                className: 'glass-section-title text-xl font-semibold mb-3',
                id: 'control-panel-main-heading'
            }, 'Design Controls'),
            // SCROLLABLE CONTROLS START
            React.createElement('div', {
                ref: scrollableContainerRef,
                className: 'design-controls-scrollable flex flex-col gap-3 overflow-y-auto overflow-x-visible flex-grow min-h-0',
                style: { maxHeight: 'calc(100vh - 12rem)' },
                'aria-label': 'Scrollable design controls area'
            },
                // Basic Controls Section (expanded by default)
                React.createElement(CollapsibleSection, {
                    title: 'Basic Controls',
                    icon: 'solar:magic-stick-3-bold',
                    defaultExpanded: true,
                    id: 'basic-controls-collapsible-section'
                }, basicControlsSection),
                // Text Settings Section (conditionally visible, expanded by default if text overlay is enabled)
                textOverlay && React.createElement(CollapsibleSection, {
                    title: 'Text Settings',
                    icon: 'solar:text-bold-duotone',
                    defaultExpanded: true,
                    id: 'text-settings-collapsible-section'
                }, textSettingsSection),
                // Person Settings Section (conditionally visible, expanded by default if include person is enabled)
                includePerson && React.createElement(CollapsibleSection, {
                    title: 'Person Settings',
                    icon: 'solar:user-bold-duotone',
                    defaultExpanded: true,
                    id: 'person-settings-collapsible-section',
                    // Dynamic max-height fix props for face upload section
                    isDynamicHeight: true,
                    isContentExpanded: Boolean(customFaceImageUrl) // Face upload is active when image/URL is present
                }, personSettingsContent),
                // Icon Rendering Mode Section (collapsed by default) - MOVED BEFORE BACKGROUND
                includeIcons && React.createElement(CollapsibleSection, {
                    title: 'Icon Rendering Style',
                    icon: 'solar:widget-5-bold-duotone',
                    defaultExpanded: false,
                    id: 'icon-rendering-mode-collapsible-section'
                }, createIconRenderingModeSelector())
            )
            // SCROLLABLE CONTROLS END
        )
    );
};

// Export the component for dynamic import
export default ControlPanel; 